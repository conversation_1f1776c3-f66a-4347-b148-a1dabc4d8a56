const TrendingTopics = () => {
  const categories = [
    {
      name: "HTML",
      slug: "html",
      count: 7,
      image: "https://ghost.estudiopatagon.com/zento/content/images/size/w300h300/2024/02/html-icon.svg",
      className: "ctag-2-html"
    },
    {
      name: "Fundamentals", 
      slug: "fundamentals",
      count: 4,
      image: "https://ghost.estudiopatagon.com/zento/content/images/size/w300h300/2024/02/code-icon.svg",
      className: "ctag-1-fundamentals"
    },
    {
      name: "CSS",
      slug: "css", 
      count: 3,
      image: "https://ghost.estudiopatagon.com/zento/content/images/size/w300h300/2024/02/css-icon.svg",
      className: "ctag-3-css"
    },
    {
      name: "Deployment",
      slug: "deployment",
      count: 2, 
      image: "https://ghost.estudiopatagon.com/zento/content/images/size/w300h300/2024/02/deploy-icon.svg",
      className: "ctag-6-deployment"
    },
    {
      name: "Databases",
      slug: "databases",
      count: 2,
      image: "https://ghost.estudiopatagon.com/zento/content/images/size/w300h300/2024/02/database-icon.svg", 
      className: "ctag-5-databases"
    },
    {
      name: "Javascript",
      slug: "javascript",
      count: 2,
      image: "https://ghost.estudiopatagon.com/zento/content/images/size/w300h300/2024/02/js02-svgrepo-com--1-.svg",
      className: "ctag-4-javascript"
    }
  ];

  return (
    <div className="section section np-bottom">
      <section className="epcl-popular-categories">
        <div className="grid-container grid-medium np-mobile">
          <h2 className="title bordered medium textcenter">
            <svg className="icon large secondary-color">
              <use xlinkHref="#trending-icon"></use>
            </svg>
            {' '}Trending Topics
          </h2>
          <div className="epcl-flex bg-box section">
            <div className="left epcl-flex grid-60 np-mobile">
              {categories.map((category, index) => (
                <div key={index} className={`item grid-20 mobile-grid-33 ${category.className} overlay-effect`}>
                  <div className="image-container">
                    <span className={`category-image ${category.className}`}>
                      <img 
                        fetchpriority="low" 
                        decoding="async" 
                        loading="lazy" 
                        src={category.image} 
                        alt={category.name} 
                        className="cover"
                      />
                    </span>
                    <span className="epcl-decoration-counter">{category.count}</span>
                  </div>
                  <h3 className="title usmall">{category.name}</h3>
                  <a href={`#${category.slug}`} className="full-link">
                    <span className="screen-reader-text">{category.name}</span>
                  </a>
                </div>
              ))}
            </div>
            <div className="right grid-40 hide-on-mobile hide-on-tablet">
              <span className="fw-bold">or...</span>
              <a href="#explore-all" className="epcl-button">Explore All</a>
            </div>
            <div className="clear"></div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TrendingTopics;
