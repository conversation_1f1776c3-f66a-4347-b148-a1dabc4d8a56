const Sidebar = () => {
  const tags = [
    { name: "Inspiration", url: "#", color: "#8665F0" },
    { name: "Lifestyle", url: "#", color: "#4EC1D8" },
    { name: "Music", url: "#", color: "#F976D0" },
    { name: "<PERSON>", url: "#", color: "#60CEB4" },
    { name: "Technology", url: "#", color: "#328AFD" },
    { name: "Travel", url: "#", color: "#F976D0" }
  ];

  const latestArticles = [
    {
      title: "Far far away, behind the word mountains",
      image: "https://ghost.estudiopatagon.com/zento-personal/content/images/size/w120h120/2024/02/66017f14-c1b4-4033-a177-8615bbfc184a-1.webp",
      date: "Mar 19, 2023",
      url: "#"
    },
    {
      title: "Building your audience with subscriber signups",
      image: "https://ghost.estudiopatagon.com/zento-personal/content/images/size/w120h120/2024/02/af4c0655-3360-41c5-997f-54b03ad540c9.webp",
      date: "Jul 25, 2022",
      url: "#"
    },
    {
      title: "Customizing your brand and design settings",
      image: "https://ghost.estudiopatagon.com/zento-personal/content/images/size/w120h120/2024/02/fae9a49c-2b52-4e0e-93fe-e071feb02042-3.webp",
      date: "Jul 20, 2022",
      url: "#"
    }
  ];

  return (
    <aside className="sidebar grid-30 tablet-grid-100 mobile-grid-100">
      {/* About Me Widget */}
      <div className="widget widget_epcl_about bg-box">
        <h3 className="widget-title title medium bordered">
          <svg className="decoration">
            <use xlinkHref="#title-decoration"></use>
          </svg>
          <span>About Me</span>
        </h3>
        <div className="about-author">
          <div className="author-info epcl-flex">
            <a href="#" className="author-image">
              <img 
                className="cover" 
                loading="lazy" 
                src="https://ghost.estudiopatagon.com/zento-personal/content/images/size/w120h120/2024/02/Rectangle-660-3.jpg" 
                alt="Jonathan Doe"
                width="60"
                height="60"
              />
              <span className="name">Jonathan Doe</span>
            </a>
            <div className="author-details">
              <h3 className="author-name">
                <a href="#">Jonathan Doe</a>
              </h3>
              <p className="author-location">Canada</p>
            </div>
          </div>
          <p className="author-bio">
            Hello! My name is Jonathan Doe!, Actively writing articles for this website. 
            I really like tutorials and illustrations, so stay alert for my next tutorials.
          </p>
        </div>
      </div>

      {/* Tag Cloud Widget */}
      <div className="widget widget_tag_cloud bg-box">
        <h3 className="widget-title title medium bordered">
          <svg className="decoration" viewBox="0 0 24 24" width="18" height="18">
            <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          <span>Tag Cloud</span>
        </h3>
        <div className="tag-cloud">
          {tags.map((tag, index) => (
            <a 
              key={index}
              href={tag.url} 
              className={`ctag ctag-${tag.name.toLowerCase()}`}
              style={{ backgroundColor: tag.color }}
            >
              {tag.name}
            </a>
          ))}
        </div>
      </div>

      {/* Latest Articles Widget */}
      <div className="widget widget_recent_entries bg-box">
        <h3 className="widget-title title medium bordered">
          <svg className="decoration" viewBox="0 0 24 24" width="18" height="18">
            <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          <span>Latest Articles</span>
        </h3>
        <div className="recent-posts">
          {latestArticles.map((article, index) => (
            <article key={index} className="recent-post">
              <div className="post-content epcl-flex">
                <a href={article.url} className="post-image">
                  <img 
                    className="cover" 
                    loading="lazy" 
                    src={article.image} 
                    alt={article.title}
                    width="60"
                    height="60"
                  />
                  <span className="screen-reader-text">{article.title}</span>
                </a>
                <div className="post-info">
                  <h4 className="post-title">
                    <a href={article.url}>{article.title}</a>
                  </h4>
                  <time className="post-date">
                    <svg className="icon" viewBox="0 0 24 24" width="14" height="14">
                      <path fill="currentColor" d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                    </svg>
                    {article.date}
                  </time>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
