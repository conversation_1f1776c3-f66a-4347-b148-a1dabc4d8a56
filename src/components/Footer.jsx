import { useState } from 'react'

const Footer = () => {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e) => {
    e.preventDefault()
    if (email) {
      setIsSubmitted(true)
      setTimeout(() => setIsSubmitted(false), 3000)
      setEmail('')
    }
  }

  const socialLinks = [
    {
      name: "WhatsApp",
      url: "https://wa.me/5492996155777",
      icon: (
        <svg className="icon">
          <use xlinkHref="#whatsapp-icon"></use>
        </svg>
      )
    },
    {
      name: "Instagram",
      url: "https://www.instagram.com/estudiopatagon/",
      icon: (
        <svg className="icon">
          <use xlinkHref="#instagram-icon"></use>
        </svg>
      )
    },
    {
      name: "Facebook",
      url: "https://www.facebook.com/ghost",
      icon: (
        <svg className="icon small">
          <use xlinkHref="#facebook-icon"></use>
        </svg>
      )
    },
    {
      name: "Twitter",
      url: "https://twitter.com/tryghost",
      icon: (
        <svg className="icon small">
          <use xlinkHref="#twitter-icon"></use>
        </svg>
      )
    }
  ]

  const quickLinks = [
    { name: "Membership", url: "#" },
    { name: "Author Page", url: "#" },
    { name: "Sign In", url: "#" },
    { name: "Subscribe / Sign Up", url: "#" },
    { name: "404 Page", url: "#" }
  ]

  return (
    <footer id="footer">
      <div className="widgets grid-container">
        <div className="desktop-footer hide-on-mobile hide-on-tablet">
          {/* Social Widget */}
          <div className="widget widget_epcl_social widget_menu grid-33 tablet-grid-50 mobile-grid-100">
            <h3 className="widget-title title medium bordered">
              <svg className="decoration">
                <use xlinkHref="#title-decoration"></use>
              </svg>
              <span>Follow Us</span>
            </h3>
            <ul className="icons">
              {socialLinks.map((social, index) => (
                <li key={index}>
                  <a href={social.url} className={social.name.toLowerCase()} target="_blank" rel="noopener noreferrer">
                    <span className="name">Follow on <b>{social.name}</b></span>
                    <span className="icon">{social.icon}</span>
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links Widget */}
          <section className="widget widget_menu grid-33 tablet-grid-50 mobile-grid-100">
            <h2 className="widget-title title medium bordered">
              <svg className="decoration" viewBox="0 0 24 24" width="18" height="18">
                <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span>Quick Links</span>
            </h2>
            <nav className="secondary-nav grid-container grid-small textcenter">
              <ul className="menu">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <span className="sep"></span>
                    <a href={link.url}>{link.name}</a>
                  </li>
                ))}
              </ul>
            </nav>
            <div className="clear"></div>
          </section>

          {/* Logo and Subscribe Widget */}
          <section className="widget widget_text grid-33 tablet-grid-50 mobile-grid-100">
            <div className="logo">
              <a href="#">
                <img
                  src="https://ghost.estudiopatagon.com/zento-personal/content/images/2024/02/logo-zento-personal-1.svg"
                  alt="Zento"
                  width="170"
                  height="60"
                  decoding="async"
                />
              </a>
            </div>
            <div className="textwidget">
              <p>Subscribe to our email newsletter and unlock access to <b>members-only</b> content and <b>exclusive updates.</b></p>
            </div>
            <br />
            <form className="subscribe-form" onSubmit={handleSubmit}>
              <label className="title small">Let's connect</label>
              <div className="form-group">
                <input
                  type="email"
                  name="email"
                  className="inputbox large"
                  required
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
                <button className="epcl-button submit absolute" type="submit">
                  Get Started
                  <span className="loader"></span>
                </button>
              </div>
              {isSubmitted && (
                <p className="success-message">
                  Subscription was sent successfully, check your email <i className="fa fa-envelope-o"></i>
                </p>
              )}
            </form>
            <div className="clear"></div>
          </section>

          <div className="clear"></div>
        </div>

        {/* Mobile Footer */}
        <div className="mobile-footer hide-on-desktop">
          <section className="widget widget_menu grid-33 tablet-grid-50 mobile-grid-100">
            <h2 className="widget-title title medium bordered">
              <svg className="decoration" viewBox="0 0 24 24" width="18" height="18">
                <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span>Quick Links</span>
            </h2>
            <nav className="secondary-nav grid-container grid-small textcenter">
              <ul className="menu">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <span className="sep"></span>
                    <a href={link.url}>{link.name}</a>
                  </li>
                ))}
              </ul>
            </nav>
            <div className="clear"></div>
          </section>

          <section className="widget widget_text grid-33 tablet-grid-50 mobile-grid-100">
            <div className="logo">
              <a href="#">
                <img
                  src="https://ghost.estudiopatagon.com/zento-personal/content/images/2024/02/logo-zento-personal-1.svg"
                  alt="Zento"
                  width="170"
                  height="60"
                  decoding="async"
                />
              </a>
            </div>
            <div className="textwidget">
              <p>Subscribe to our email newsletter and unlock access to <b>members-only</b> content and <b>exclusive updates.</b></p>
            </div>
            <br />
            <form className="subscribe-form" onSubmit={handleSubmit}>
              <label className="title small">Let's connect</label>
              <div className="form-group">
                <input
                  type="email"
                  name="email"
                  className="inputbox large"
                  required
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
                <button className="epcl-button submit absolute" type="submit">
                  Get Started
                  <span className="loader"></span>
                </button>
              </div>
              {isSubmitted && (
                <p className="success-message">
                  Subscription was sent successfully, check your email <i className="fa fa-envelope-o"></i>
                </p>
              )}
            </form>
            <div className="clear"></div>
          </section>
        </div>
      </div>

      <p className="published underline-effect">
        <a href="https://estudiopatagon.com/projects/zento-for-ghost/" target="_blank" rel="noopener noreferrer">Zento</a> Theme by{' '}
        <a href="https://estudiopatagon.com/" target="_blank" rel="noopener noreferrer">EstudioPatagon</a>
        <span className="dot"></span> Powered by{' '}
        <a href="https://ghost.org/" target="_blank" rel="noopener noreferrer">Ghost</a>
      </p>
      <div className="clear"></div>
    </footer>
  )
}

export default Footer
