const BlogCard = ({ post }) => {
  const renderStars = (rating) => {
    if (!rating) return null;

    return (
      <div className="difficulty meta-info">
        <svg className="icon main-color">
          <use xlinkHref="#medal-icon"></use>
        </svg>
        <span className="name">Rating</span>
        <div className={`rating ${rating === 1 ? 'star-1' : rating === 2 ? 'star-2' : 'star-3'}`}>
          <span className="star">★</span>
          <span className="star">★</span>
          <span className="star">★</span>
        </div>
      </div>
    );
  };

  const renderAccessIcon = () => {
    if (post.access === 'members') {
      return (
        <a href="#" className="access-icon visibility-members meta-info tooltip hide-on-mobile" data-title="Members Article" aria-label="Members Article">
          <svg>
            <use xlinkHref="#locked"></use>
          </svg>
        </a>
      );
    }

    if (post.access === 'tiers' || post.access === 'premium') {
      return (
        <>
          <a href="#" className="access-icon visibility-paid meta-info tooltip hide-on-mobile" data-title="Premium Article" aria-label="Premium Article">
            <svg>
              <use xlinkHref="#vip"></use>
            </svg>
          </a>
          {post.featured && (
            <a href="#" className="access-icon visibility-members meta-info tooltip hide-on-mobile" data-title="Featured Article" aria-label="Featured Article">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M11.9998 17L6.12197 20.5902L7.72007 13.8906L2.48926 9.40983L9.35479 8.85942L11.9998 2.5L14.6449 8.85942L21.5104 9.40983L16.2796 13.8906L17.8777 20.5902L11.9998 17Z"/>
              </svg>
            </a>
          )}
        </>
      );
    }

    return null;
  };

  return (
    <article className={`default classic-large bg-box ctag ctag-${post.category.toLowerCase()} post-access-${post.access}`}>
      <div className="post-format-image epcl-flex">
        <div className="featured-image">
          <a href="#" className="thumb epcl-loader opacity-effect">
            <img
              className="fullimage cover"
              loading="lazy"
              fetchpriority="low"
              decoding="async"
              src={post.image}
              alt={`Image of: ${post.title}`}
            />
            <span className="screen-reader-text">{post.title}</span>
          </a>
          <div className="tags fill-color absolute">
            <a
              href="#"
              className={`primary-tag ctag ctag-${post.category.toLowerCase()}`}
              data-id={`ctag-${post.category.toLowerCase()}`}
              style={{ backgroundColor: post.categoryColor }}
            >
              {post.category}
            </a>
          </div>
        </div>

        <div className="info">
          <header>
            <div className="meta inline small">
              <span className="hide-on-desktop-sm hide-on-tablet hide-on-mobile">
                <time className="meta-info" dateTime={post.date}>
                  <svg className="icon main-color small">
                    <use xlinkHref="#calendar-icon"></use>
                  </svg>
                  {post.date}
                </time>
              </span>

              <div className="min-read meta-info">
                <svg className="icon main-color">
                  <use xlinkHref="#reading-icon"></use>
                </svg>
                {post.readTime}
              </div>

              {renderStars(post.rating)}
            </div>
            <h2 className="main-title title underline-effect">
              <a href="#">{post.title}</a>
            </h2>

            {renderAccessIcon()}
          </header>
          
          <div className="post-excerpt">
            <p>{post.excerpt}</p>
            <div className="clear"></div>
          </div>
          
          <footer className="bottom">
            <div className="meta bottom epcl-flex">
              <div className="tags">
                {post.tags.map((tag, index) => (
                  <a key={index} href="#" className={`ctag ctag-${tag.toLowerCase()}`}>
                    {tag}
                  </a>
                ))}
              </div>
              <a href="#" className="author">
                <img 
                  className="author-image cover" 
                  loading="lazy" 
                  src={post.author.image} 
                  alt={`Image of: ${post.author.name}`}
                />
                <span className="author-name">{post.author.name}</span>
              </a>
            </div>
            <div className="clear"></div>
          </footer>
        </div>
      </div>
      <div className="clear"></div>
    </article>
  );
};

export default BlogCard;
